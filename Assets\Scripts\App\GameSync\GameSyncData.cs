using App.Components.UI.Timeline;
using App.Models.SecondSpectrum.Impl;
using CoreTech.Promises;
using UnityEngine;

namespace App.GameSync
{
    public class GameSyncData : MonoBehaviour
    {
        private string gameId;
        private long binaryId;
        private string baseUrl;
        private SecondSpectrumFrameModel secondSpectrumFrameModel;

        private void Awake()
        {
            // TODO : Replace with a more robust way to get these values perhaps CI/CD variables or a config file
            gameId = "2292817_batch";
            if (string.IsNullOrEmpty(gameId))
            {
                Debug.LogError("GameId is not set in PlayerPrefs. Please set it before using GameSyncData.");
            }

            binaryId = long.Parse("1659794539000");
            if (binaryId == 0)
            {
                Debug.LogError("BinaryId is not set in PlayerPrefs. Please set it before using GameSyncData.");
            }

            baseUrl =  "https://d2grtshlowl8y9.cloudfront.net";
            if (string.IsNullOrEmpty(baseUrl))
            {
                Debug.LogError("BaseURL is not set in PlayerPrefs. Please set it before using GameSyncData.");
            }
        }

        public IPromise StartFetching(SecondSpectrumFrameModel secondSpectrumFrameModel)
        {
            this.secondSpectrumFrameModel = secondSpectrumFrameModel;
            var url = $"{baseUrl}/{gameId}/tracking-pose/";
            return secondSpectrumFrameModel
                .StartFetching(binaryId, url)
                .Then(() => Debug.Log("Fetching Initial Binary Data Completed"));
        }

        public void InitTimeline(TimelineDataService timelineDataService)
        {
            timelineDataService.Initialize(binaryId);
        }

        public void NextBinary(long nextBinaryId)
        {
            secondSpectrumFrameModel.SetNextBinaryId(nextBinaryId);
        }
    }
}