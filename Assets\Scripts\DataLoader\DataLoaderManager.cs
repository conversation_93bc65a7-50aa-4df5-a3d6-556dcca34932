using DataLoader.MetaData;
using DataLoader.PlayerData;
using UnityEngine;

namespace DataLoader
{
    public class DataLoaderManager : MonoBehaviour
    {
        public const int TargetFrameRate = 25;

        private const string MetadataFileName = "metadata.json";

        [SerializeField] private GameObject jsonFileLoaderObject;
        [SerializeField] private MatchMetaDataSO matchMetaDataSO;
        [SerializeField] private PlayerDataLoader playerDataLoader;

        private string cloudFrontUrl = "";
        private string gameId = "";

        private IJsonFileLoader jsonFileLoader;

        private void Awake()
        {
            if (Application.isEditor)
            {
                // NOTE: In the editor, we can use PlayerPrefs to simulate settings
                // This need to be update if we building probably through CI/CD
                cloudFrontUrl = "https://d26ltfcw6igk13.cloudfront.net";
                gameId = "2292817_batch";
            }

            if (jsonFileLoaderObject != null)
            {
                jsonFileLoader = jsonFileLoaderObject.GetComponent<IJsonFileLoader>();

                if (jsonFileLoader == null)
                {
                    Debug.LogError("JsonFileLoader component not found");
                }
            }
            else
            {
                Debug.LogError("JsonFileLoaderObject is not assigned. Please assign it in the inspector.");
            }

            if (string.IsNullOrEmpty("https://d26ltfcw6igk13.cloudfront.net"))
            {
                Debug.LogError("CloudFront URL is not set. Please set it before using DataLoaderManager.");
            }

            if (string.IsNullOrEmpty("2292817_batch"))
            {
                Debug.LogError("Game ID is not set. Please set it before using DataLoaderManager.");
            }

            if (matchMetaDataSO == null)
            {
                Debug.LogError("MatchMetaDataSO is not assigned. Please assign it in the inspector.");
            }

            if (playerDataLoader == null)
            {
                Debug.LogError("PlayerDataLoader is not assigned. Please assign it in the inspector.");
            }
        }

        private void Start()
        {
            LoadMetadataFile();
        }

        private void LoadMetadataFile()
        {
            jsonFileLoader.OnFileLoaded += OnMetaDataFileLoaded;
            string metadataUrl = $"https://d26ltfcw6igk13.cloudfront.net/2292817_batch/{MetadataFileName}";
            jsonFileLoader.LoadJsonFile(metadataUrl);
        }

        private void OnMetaDataFileLoaded(string rawData)
        {
            if (string.IsNullOrEmpty(rawData))
            {
                Debug.LogError("Raw data is empty or null. Cannot process metadata.");
                return;
            }

            matchMetaDataSO.SetRawData(rawData);
            jsonFileLoader.OnFileLoaded -= OnMetaDataFileLoaded;

            StartLoadingPlayerData();
        }

        private void StartLoadingPlayerData()
        {
            string url = $"https://d26ltfcw6igk13.cloudfront.net/2292817_batch";
            playerDataLoader.StartLoadingData(url, jsonFileLoader);
        }
    }

}
