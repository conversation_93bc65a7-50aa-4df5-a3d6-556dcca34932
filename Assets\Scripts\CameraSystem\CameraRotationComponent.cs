using Unity.Cinemachine;
using UnityEngine;
using UnityEngine.EventSystems;

namespace CameraSystem
{
    public class CameraRotationComponent : MonoBehaviour
    {
        [Header("Camera Settings")]
        [SerializeField] private CinemachineCamera targetCamera;
        
        [Header("Rotation Settings")]
        [SerializeField] private float mouseSensitivity = 2f;

        [Header("Input Settings")]
        [SerializeField] private bool enableMouseInput = true;
        [SerializeField] private bool enableTouchInput = true;
        
        private CinemachineOrbitalFollow orbitalFollow;
        private bool isDragging = false;
        private Vector2 lastMousePosition;

        private const float HorizontalMultiplier = 0.1f;
        private const float VerticalMultiplier = 0.001f;
        
        private void Start()
        {
            InitializeCamera();
        }
        
        private void InitializeCamera()
        {
            if (targetCamera == null)
            {
                targetCamera = GetComponent<CinemachineCamera>();
            }
            
            if (targetCamera == null)
            {
                Debug.LogError("CameraRotationComponent: No CinemachineCamera found!");
                return;
            }
            
            orbitalFollow = targetCamera.GetComponent<CinemachineOrbitalFollow>();
            if (orbitalFollow == null)
            {
                Debug.LogError("CameraRotationComponent: CinemachineOrbitalFollow component not found on camera!");
                return;
            }
            
            Debug.Log("CameraRotationComponent: Initialized successfully");
        }
        
        private void Update()
        {
            if (orbitalFollow == null) return;
            
            HandleInput();
        }
        
        private void HandleInput()
        {
            if (enableTouchInput)
            {
                HandleTouchInput();
            }
            
            if (enableMouseInput)
            {
                HandleMouseInput();
            }
        }
        
        private void HandleMouseInput()
        {
            if (EventSystem.current != null && EventSystem.current.IsPointerOverGameObject()) // Function name is misleading, it checks if the pointer is over any UI element only
                return;

            if (Input.GetMouseButtonDown(0))
            {
                StartRotation();
                lastMousePosition = Input.mousePosition;
            }
            else if (Input.GetMouseButtonUp(0))
            {
                StopRotation();
            }

            if (isDragging)
            {
                Vector2 currentMousePosition = Input.mousePosition;
                Vector2 mouseDelta = currentMousePosition - lastMousePosition;

                RotateCamera(-mouseDelta.x, -mouseDelta.y);

                lastMousePosition = currentMousePosition;
            }
        }
        
        private void HandleTouchInput()
        {
            if (Input.touchCount == 1)
            {
                Touch touch = Input.GetTouch(0);
                if (EventSystem.current != null && EventSystem.current.IsPointerOverGameObject(touch.fingerId))
                    return;
                
                if (touch.phase == TouchPhase.Began)
                {
                    StartRotation();
                    lastMousePosition = touch.position;
                }
                else if (touch.phase == TouchPhase.Ended || touch.phase == TouchPhase.Canceled)
                {
                    StopRotation();
                }
                else if (touch.phase == TouchPhase.Moved && isDragging)
                {
                    if (EventSystem.current != null && EventSystem.current.IsPointerOverGameObject(touch.fingerId))
                    {
                        StopRotation();
                        return;
                    }

                    Vector2 touchDelta = touch.position - lastMousePosition;
                    RotateCamera(-touchDelta.x, -touchDelta.y);
                    lastMousePosition = touch.position;
                }
            }
            else if (Input.touchCount > 1)
            {
                StopRotation();
            }
        }
        
        private void StartRotation()
        {
            isDragging = true;
        }

        private void StopRotation()
        {
            isDragging = false;
        }

        private void OnDisable()
        {
            isDragging = false;
        }
        
        public void RotateCamera(float horizontalDelta, float verticalDelta)
        {
            if (orbitalFollow == null) return;

            float newHorizontalValue = orbitalFollow.HorizontalAxis.Value + (horizontalDelta * mouseSensitivity * HorizontalMultiplier);
            orbitalFollow.HorizontalAxis.Value = newHorizontalValue;

            float newVerticalValue = orbitalFollow.VerticalAxis.Value + (verticalDelta * mouseSensitivity * VerticalMultiplier);
            newVerticalValue = Mathf.Clamp(newVerticalValue, orbitalFollow.VerticalAxis.Range.x, orbitalFollow.VerticalAxis.Range.y);
            orbitalFollow.VerticalAxis.Value = newVerticalValue;
        }
    }
}
